from __future__ import annotations
import asyncio
import json
import sys

from typing import Optional, List, Any, Dict
from contextlib import AsyncExitStack
from urllib.parse import urlparse

from mcp import ClientSession
from mcp.client.streamable_http import streamablehttp_client
from mcp.types import TextContent

from openai import AsyncOpenAI
from openai.types.chat import ChatCompletion

from dotenv import load_dotenv

load_dotenv()  # Load environment variables from .env file

MODEL = "gpt-4o-mini"


class GPTClient:

    def __init__(self):
        self.session: Optional[ClientSession] = None
        self.exit_stack = AsyncExitStack()
        self.openai = AsyncOpenAI()
        self.conversation_history = []

    async def connect(self, server_url: str):
        """Connect to MCP server over HTTP transport
        
        Args:
					server_url: HTTP URL of the MCP server (e.g., 'http://localhost:8000/mcp')
        """
        try:
            parsed = urlparse(server_url)
            if not parsed.scheme or not parsed.netloc:
                raise ValueError(f"Invalid server URL: {server_url}")
            if parsed.scheme not in ("http", "https"):
                raise ValueError("URL scheme must be http or https")
        except Exception as e:
            raise ValueError(f"Invalid server URL '{server_url}': {e}")
        
        try:
            print("Establishing HTTP transport connection...")
            http_transport = await asyncio.wait_for(
                self.exit_stack.enter_async_context(streamablehttp_client(server_url)),
                timeout=30.0
            )
            self.read_stream, self.write_stream, self.session_id = http_transport

            print("Creating MCP session...")
            self.session = await self.exit_stack.enter_async_context(ClientSession(self.read_stream, self.write_stream))

            print("Initializing session...")
            await asyncio.wait_for(self.session.initialize(), timeout=10.0)  # Wait for initialization to complete within 10 seconds

            print("Discovering available tools...")
            toolsData = await self.session.list_tools()
            tools = toolsData.tools
            print(f"\n✅ Connected to MCP server at {server_url}")
            print(f"📋 Available tools: {[tool.name for tool in tools]}")

        except Exception as e:
            raise RuntimeError(f"Failed to connect to MCP server at {server_url}: {e}")

    async def disconnect(self):
        pass

    async def query(self, query: str) -> str:
        pass

    async def chat(self):
        pass


async def main():
    if len(sys.argv) == 1:
        server_url = "http://localhost:8000/mcp"
        print(f"No server URL provided, using default: {server_url}")
    elif len(sys.argv) == 2:
        server_url = sys.argv[1]
    else:
        print("Usage: python gptclient.py [server_url]")
        print("Examples:")
        print("  python gptclient.py")
        print("  python gptclient.py http://localhost:8000/mcp")
        print("  python gptclient.py https://your-server.com/mcp")
        sys.exit(1)

    client = GPTClient()
    try:
        print(f"Connecting to MCP server at {server_url}...")
        await client.connect(server_url)
        await client.chat()
    except KeyboardInterrupt:
        print("\n\nInterrupted by user")
    except Exception as e:
        print(f"Error: {e}")
        print("\nTroubleshooting:")
        print("1. Make sure the MCP server is running")
        print("2. Verify the server URL is correct")
        print("3. Check that the server supports HTTP transport")
        print("4. Ensure your ANTHROPIC_API_KEY environment variable is set")
    finally:
        await client.disconnect()


if __name__ == "__main__":
    asyncio.run(main())
